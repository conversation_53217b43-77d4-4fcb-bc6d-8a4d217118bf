{"buildFiles": ["D:\\1111\\flutter_windows_3.29.3-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\flutter_application_ss\\android\\app\\.cxx\\Debug\\3c6r3325\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\1111\\flutter_windows_3.29.3-stable\\ss\\fff\\44\\vv\\price_ss\\flutter_application_ss\\android\\app\\.cxx\\Debug\\3c6r3325\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}