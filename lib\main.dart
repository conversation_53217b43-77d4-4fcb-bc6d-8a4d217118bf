import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'تطبيق المشتريات',
      theme: ThemeData(primarySwatch: Colors.blue, fontFamily: 'Cairo'),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

// نموذج المستخدم
class AppUser {
  final String id;
  final String name;
  final String code;

  AppUser({required this.id, required this.name, required this.code});
}

// نموذج المشتريات
class Purchase {
  final String id;
  final String userId;
  final String itemName;
  final double price;
  final DateTime date;

  Purchase({
    required this.id,
    required this.userId,
    required this.itemName,
    required this.price,
    required this.date,
  });

  factory Purchase.fromMap(String id, Map<String, dynamic> map) {
    return Purchase(
      id: id,
      userId: map['userId'] ?? '',
      itemName: map['itemName'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      date:
          map['date'] != null
              ? (map['date'] as Timestamp).toDate()
              : DateTime.now(),
    );
  }
}

// إضافة نموذج للمشتريات المعلقة
class PendingPurchase {
  final String userId;
  final String itemName;
  final double price;
  final DateTime createdAt;

  PendingPurchase({
    required this.userId,
    required this.itemName,
    required this.price,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'itemName': itemName,
      'price': price,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory PendingPurchase.fromJson(Map<String, dynamic> json) {
    return PendingPurchase(
      userId: json['userId'],
      itemName: json['itemName'],
      price: json['price'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
    );
  }
}

// خدمة قاعدة البيانات
class DatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // تهيئة المستخدمين
  Future<void> initializeUsers() async {
    try {
      // التحقق أولاً من وجود مستخدمين
      final usersSnapshot = await _firestore.collection('users').get();

      // إذا كان هناك مستخدمين بالفعل، قم بحذفهم جميع<|im_start|>
      if (usersSnapshot.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (var doc in usersSnapshot.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
      }

      // إضافة المستخدمين الجدد بالأسماء الصحيحة
      final users = [
        {'name': 'بشر', 'code': '1111'},
        {'name': 'محمود', 'code': '2222'},
        {'name': 'مجد', 'code': '3333'},
        {'name': 'مراد', 'code': '4444'},
      ];

      // استخدام batch للإضافة بدلاً من إضافة كل مستخدم على حدة
      final batch = _firestore.batch();
      for (var userData in users) {
        final docRef = _firestore.collection('users').doc(); // إنشاء معرف جديد
        batch.set(docRef, userData);
      }
      await batch.commit();

      debugPrint('تم تهيئة المستخدمين بنجاح');
    } catch (e) {
      debugPrint('خطأ في تهيئة المستخدمين: $e');
    }
  }

  // الحصول على المستخدمين
  Future<List<AppUser>> getUsers() async {
    try {
      final snapshot = await _firestore.collection('users').get();

      // إزالة المستخدمين المكررين باستخدام مجموعة لتخزين الأسماء التي تمت معالجتها
      Set<String> processedNames = {};
      List<AppUser> uniqueUsers = [];

      for (var doc in snapshot.docs) {
        final data = doc.data();
        final name = data['name'] as String;

        // إذا لم تتم معالجة هذا الاسم من قبل، أضفه إلى القائمة
        if (!processedNames.contains(name)) {
          processedNames.add(name);
          uniqueUsers.add(
            AppUser(id: doc.id, name: name, code: data['code'] as String),
          );
        }
      }

      return uniqueUsers;
    } catch (e) {
      debugPrint('خطأ في الحصول على المستخدمين: $e');
      return [];
    }
  }

  // إضافة مشتريات
  Future<bool> addPurchase(String userId, String itemName, double price) async {
    try {
      // التحقق من الاتصال بالإنترنت
      bool isConnected = await _checkInternetConnection();

      if (isConnected) {
        // إذا كان متصلاً، أضف المشتريات مباشرة
        await _firestore.collection('purchases').add({
          'userId': userId,
          'itemName': itemName,
          'price': price,
          'date': FieldValue.serverTimestamp(),
        });

        // تحقق من وجود مشتريات معلقة وأضفها
        await _syncPendingPurchases();

        debugPrint('تمت إضافة المشتريات بنجاح');
        return true;
      } else {
        // إذا كان غير متصل، احفظ المشتريات محلي
        await savePendingPurchase(userId, itemName, price);
        debugPrint('تم حفظ المشتريات محلي');
        return true;
      }
    } catch (e) {
      debugPrint('خطأ في إضافة المشتريات: $e');
      return false;
    }
  }

  // الحصول على مشتريات مستخدم
  Future<List<Purchase>> getUserPurchases(String userId) async {
    try {
      final snapshot =
          await _firestore
              .collection('purchases')
              .where('userId', isEqualTo: userId)
              .orderBy('date', descending: true)
              .get();

      return snapshot.docs.map((doc) {
        try {
          return Purchase.fromMap(doc.id, doc.data());
        } catch (e) {
          debugPrint('خطأ في تحويل وثيقة المشتريات: $e');
          return Purchase(
            id: doc.id,
            userId: userId,
            itemName: 'خطأ في البيانات',
            price: 0,
            date: DateTime.now(),
          );
        }
      }).toList();
    } catch (e) {
      debugPrint('خطأ في استرجاع مشتريات المستخدم: $e');
      return [];
    }
  }

  // الحصول على جميع المشتريات
  Future<List<Purchase>> getAllPurchases() async {
    try {
      final snapshot =
          await _firestore
              .collection('purchases')
              .orderBy('date', descending: true)
              .get();

      return snapshot.docs
          .map((doc) => Purchase.fromMap(doc.id, doc.data()))
          .toList();
    } catch (e) {
      debugPrint('خطأ في استرجاع جميع المشتريات: $e');
      return [];
    }
  }

  // حذف جميع المشتريات
  Future<void> deleteAllPurchases() async {
    try {
      final batch = _firestore.batch();
      final snapshot = await _firestore.collection('purchases').get();

      for (var doc in snapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();

      // إعادة تعيين تصويتات التصفية
      await _firestore.collection('reset_votes').doc('votes').set({
        'users': [],
      });

      debugPrint('تم حذف جميع المشتريات بنجاح');
    } catch (e) {
      debugPrint('خطأ في حذف المشتريات: $e');
    }
  }

  // إضافة تصويت لتصفية الحسابات
  Future<Map<String, dynamic>> addResetVote(String userId) async {
    try {
      final votesRef = _firestore.collection('reset_votes').doc('votes');
      final votesDoc = await votesRef.get();

      if (!votesDoc.exists) {
        await votesRef.set({
          'users': [userId],
        });
        return {'success': true, 'count': 1, 'total': 4, 'completed': false};
      }

      List<String> voters = List<String>.from(votesDoc.data()?['users'] ?? []);

      if (voters.contains(userId)) {
        return {
          'success': false,
          'message': 'لقد قمت بالتصويت بالفعل',
          'count': voters.length,
          'total': 4,
          'completed': false,
        };
      }

      voters.add(userId);
      await votesRef.update({'users': voters});

      bool completed = voters.length >= 4;

      return {
        'success': true,
        'count': voters.length,
        'total': 4,
        'completed': completed,
      };
    } catch (e) {
      debugPrint('خطأ في إضافة تصويت: $e');
      return {
        'success': false,
        'message': 'حدث خطأ أثناء التصويت',
        'count': 0,
        'total': 4,
        'completed': false,
      };
    }
  }

  // الحصول على حالة تصويتات التصفية
  Future<Map<String, dynamic>> getResetVotes() async {
    try {
      final votesRef = _firestore.collection('reset_votes').doc('votes');
      final votesDoc = await votesRef.get();

      if (!votesDoc.exists) {
        await votesRef.set({'users': []});
        return {'count': 0, 'total': 4, 'users': []};
      }

      List<String> voters = List<String>.from(votesDoc.data()?['users'] ?? []);

      return {'count': voters.length, 'total': 4, 'users': voters};
    } catch (e) {
      debugPrint('خطأ في الحصول على التصويتات: $e');
      return {'count': 0, 'total': 4, 'users': []};
    }
  }

  // تأكد من أن هذه الدالة موجودة في كلاس DatabaseService
  Future<void> forceResetUsers() async {
    try {
      // حذف جميع المستخدمين الحاليين
      final usersSnapshot = await _firestore.collection('users').get();

      final batch = _firestore.batch();
      for (var doc in usersSnapshot.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // إضافة المستخدمين بالأسماء الصحيحة
      final users = [
        {'name': 'بشر', 'code': '1111'},
        {'name': 'محمود', 'code': '2222'},
        {'name': 'مجد', 'code': '3333'},
        {'name': 'مراد', 'code': '4444'},
      ];

      // استخدام batch للإضافة
      final addBatch = _firestore.batch();
      for (var userData in users) {
        final docRef = _firestore.collection('users').doc();
        addBatch.set(docRef, userData);
      }
      await addBatch.commit();

      debugPrint('تم إعادة تهيئة المستخدمين بنجاح');
    } catch (e) {
      debugPrint('خطأ في إعادة تهيئة المستخدمين: $e');
      rethrow; // إعادة رمي الخطأ للتعامل معه في المستدعي
    }
  }

  // إضافة دالة حذف مشتريات
  Future<bool> deletePurchase(String purchaseId) async {
    try {
      await _firestore.collection('purchases').doc(purchaseId).delete();
      debugPrint('تم حذف المشتريات بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في حذف المشتريات: $e');
      return false;
    }
  }

  // إضافة دالة تعديل مشتريات
  Future<bool> updatePurchase(
    String purchaseId,
    String itemName,
    double price,
  ) async {
    try {
      await _firestore.collection('purchases').doc(purchaseId).update({
        'itemName': itemName,
        'price': price,
      });
      debugPrint('تم تعديل المشتريات بنجاح');
      return true;
    } catch (e) {
      debugPrint('خطأ في تعديل المشتريات: $e');
      return false;
    }
  }

  // إضافة دوال للتعامل مع المشتريات المعلقة
  Future<void> savePendingPurchase(
    String userId,
    String itemName,
    double price,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // الحصول على المشتريات المعلقة الحالية
      List<String> pendingPurchasesJson =
          prefs.getStringList('pending_purchases') ?? [];
      List<PendingPurchase> pendingPurchases =
          pendingPurchasesJson
              .map((json) => PendingPurchase.fromJson(jsonDecode(json)))
              .toList();

      // إضافة المشتريات الجديدة
      pendingPurchases.add(
        PendingPurchase(
          userId: userId,
          itemName: itemName,
          price: price,
          createdAt: DateTime.now(),
        ),
      );

      // حفظ القائمة المحدثة
      pendingPurchasesJson =
          pendingPurchases
              .map((purchase) => jsonEncode(purchase.toJson()))
              .toList();

      await prefs.setStringList('pending_purchases', pendingPurchasesJson);

      debugPrint('تم حفظ المشتريات المعلقة بنجاح');
    } catch (e) {
      debugPrint('خطأ في حفظ المشتريات المعلقة: $e');
    }
  }

  Future<List<PendingPurchase>> getPendingPurchases() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> pendingPurchasesJson =
          prefs.getStringList('pending_purchases') ?? [];

      return pendingPurchasesJson
          .map((json) => PendingPurchase.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على المشتريات المعلقة: $e');
      return [];
    }
  }

  Future<void> clearPendingPurchases() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('pending_purchases');
      debugPrint('تم مسح المشتريات المعلقة بنجاح');
    } catch (e) {
      debugPrint('خطأ في مسح المشتريات المعلقة: $e');
    }
  }

  // دالة لفحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }

  // دالة لمزامنة المشتريات المعلقة مع قاعدة البيانات
  Future<void> _syncPendingPurchases() async {
    try {
      final pendingPurchases = await getPendingPurchases();
      if (pendingPurchases.isNotEmpty) {
        for (var purchase in pendingPurchases) {
          await _firestore.collection('purchases').add({
            'userId': purchase.userId,
            'itemName': purchase.itemName,
            'price': purchase.price,
            'date': FieldValue.serverTimestamp(),
          });
        }
        await clearPendingPurchases();
        debugPrint('تم مزامنة جميع المشتريات المعلقة بنجاح');
      }
    } catch (e) {
      debugPrint('خطأ في مزامنة المشتريات المعلقة: $e');
    }
  }
}

// الشاشة الرئيسية
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<AppUser> _users = [];
  bool _isLoading = true;
  Map<String, bool> _authenticatedUsers = {};
  int _resetVotesCount = 0;
  int _totalVotesNeeded = 4;

  // دالة مساعدة لإظهار SnackBar بشكل آمن
  void _showSnackBarSafely(String message, {Color? backgroundColor}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: backgroundColor),
    );
  }

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تحميل المستخدمين
      final users = await _databaseService.getUsers();

      // إذا لم يكن هناك مستخدمين أو عددهم أقل من 4، قم بتهيئتهم
      if (users.isEmpty || users.length < 4) {
        debugPrint(
          'عدد المستخدمين غير كافٍ (${users.length})، جاري إعادة التهيئة...',
        );
        await _databaseService.initializeUsers();
        final newUsers = await _databaseService.getUsers();

        setState(() {
          _users = newUsers;
        });
      } else {
        // التحقق من عدم وجود تكرار في المستخدمين
        Set<String> uniqueNames = {};
        List<AppUser> uniqueUsers = [];

        for (var user in users) {
          if (!uniqueNames.contains(user.name)) {
            uniqueNames.add(user.name);
            uniqueUsers.add(user);
          }
        }

        // إذا كان هناك تكرار أو عدد المستخدمين الفريدين أقل من 4، قم بإعادة تهيئة المستخدمين
        if (uniqueUsers.length < 4) {
          debugPrint(
            'عدد المستخدمين الفريدين غير كافٍ (${uniqueUsers.length})، جاري إعادة التهيئة...',
          );
          await _databaseService.initializeUsers();
          final newUsers = await _databaseService.getUsers();

          setState(() {
            _users = newUsers;
          });
        } else {
          setState(() {
            _users = users;
          });
        }
      }

      // تحميل حالة المصادقة للمستخدمين
      await _loadAuthenticatedUsers();

      // تحميل حالة تصويتات التصفية
      await _loadResetVotes();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تهيئة التطبيق: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // تحميل حالة المصادقة للمستخدمين
  Future<void> _loadAuthenticatedUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      Map<String, bool> authenticatedUsers = {};

      for (var user in _users) {
        bool isAuthenticated = prefs.getBool('auth_${user.id}') ?? false;
        authenticatedUsers[user.id] = isAuthenticated;
      }

      setState(() {
        _authenticatedUsers = authenticatedUsers;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل حالة المصادقة: $e');
    }
  }

  // حفظ حالة المصادقة للمستخدم
  Future<void> _saveAuthenticatedUser(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('auth_$userId', true);

      setState(() {
        _authenticatedUsers[userId] = true;
      });
    } catch (e) {
      debugPrint('خطأ في حفظ حالة المصادقة: $e');
    }
  }

  // تحميل حالة تصويتات التصفية
  Future<void> _loadResetVotes() async {
    try {
      final votesData = await _databaseService.getResetVotes();

      setState(() {
        _resetVotesCount = votesData['count'];
        _totalVotesNeeded = votesData['total'];
      });
    } catch (e) {
      debugPrint('خطأ في تحميل تصويتات التصفية: $e');
    }
  }

  // 1. إزالة دالة _resetUsers غير المستخدمة
  // حذف الدالة بالكامل

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تطبيق المشتريات'),
        actions: [
          IconButton(
            icon: const Icon(Icons.list),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AllPurchasesScreen(),
                ),
              );
            },
            tooltip: 'عرض جميع المشتريات',
          ),
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const StatisticsScreen(),
                ),
              );
            },
            tooltip: 'الإحصائيات',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  const SizedBox(height: 20),
                  const Text(
                    'المستخدمين',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: 2,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      children:
                          _users.map((user) {
                            bool isAuthenticated =
                                _authenticatedUsers[user.id] ?? false;

                            return ElevatedButton(
                              onPressed: () {
                                if (isAuthenticated) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => UserScreen(user: user),
                                    ),
                                  );
                                } else {
                                  _showLoginDialog(context, user);
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.all(16),
                                backgroundColor:
                                    isAuthenticated ? Colors.green : null,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    user.name,
                                    style: const TextStyle(fontSize: 20),
                                  ),
                                  if (isAuthenticated)
                                    const Icon(
                                      Icons.check_circle,
                                      color: Colors.white,
                                    ),
                                ],
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          Text(
                            'تصويتات تصفية الحسابات: $_resetVotesCount من $_totalVotesNeeded',
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 8),
                          LinearProgressIndicator(
                            value: _resetVotesCount / _totalVotesNeeded,
                            backgroundColor: Colors.grey[300],
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Colors.red,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: () {
                              _showResetVoteDialog(context);
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              backgroundColor: Colors.red,
                            ),
                            child: const Text('تصفية الحسابات'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  void _showLoginDialog(BuildContext context, AppUser user) {
    final TextEditingController codeController = TextEditingController();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: Text('تسجيل الدخول كـ ${user.name}'),
          content: TextField(
            controller: codeController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(labelText: 'أدخل الرمز'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed:
                  () => _handleLogin(dialogContext, user, codeController.text),
              child: const Text('دخول'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleLogin(
    BuildContext dialogContext,
    AppUser user,
    String code,
  ) async {
    if (code == user.code) {
      Navigator.pop(dialogContext);

      // حفظ حالة المصادقة
      await _saveAuthenticatedUser(user.id);

      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => UserScreen(user: user)),
      );
    } else {
      Navigator.pop(dialogContext);
      _showSnackBarSafely('رمز غير صحيح');
    }
  }

  void _showResetVoteDialog(BuildContext context) {
    final TextEditingController codeController = TextEditingController();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('تصويت على تصفية الحسابات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'للتصويت على تصفية الحسابات، يرجى إدخال رمزك الشخصي.',
                style: TextStyle(color: Colors.red),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: codeController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(labelText: 'أدخل الرمز'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed:
                  () => _handleResetVote(dialogContext, codeController.text),
              child: const Text('تصويت'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleResetVote(BuildContext dialogContext, String code) async {
    // التحقق من الرمز
    AppUser? matchedUser;
    for (var user in _users) {
      if (user.code == code) {
        matchedUser = user;
        break;
      }
    }

    // إغلاق الحوار أولاً
    Navigator.pop(dialogContext);

    if (matchedUser != null) {
      // إضافة تصويت
      final result = await _databaseService.addResetVote(matchedUser.id);

      if (!mounted) return;

      if (result['success']) {
        // تحديث عدد التصويتات
        setState(() {
          _resetVotesCount = result['count'];
        });

        // إذا اكتملت التصويتات، قم بتصفية الحسابات
        if (result['completed']) {
          await _databaseService.deleteAllPurchases();
          if (!mounted) return;
          setState(() {
            _resetVotesCount = 0;
          });

          _showSnackBarSafely('تم تصفية الحسابات بنجاح');
        } else {
          _showSnackBarSafely(
            'تم التصويت بنجاح. عدد التصويتات: ${result['count']} من ${result['total']}',
          );
        }
      } else {
        _showSnackBarSafely(result['message']);
      }
    } else {
      _showSnackBarSafely('رمز غير صحيح');
    }
  }
}

// شاشة المستخدم
class UserScreen extends StatefulWidget {
  final AppUser user;

  const UserScreen({super.key, required this.user});

  @override
  State<UserScreen> createState() => _UserScreenState();
}

class _UserScreenState extends State<UserScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final TextEditingController _itemNameController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  List<Purchase> _purchases = [];
  bool _isLoading = true;
  List<PendingPurchase> _pendingPurchases = [];

  // دالة مساعدة لإظهار SnackBar بشكل آمن
  void _showSnackBarSafely(String message, {Color? backgroundColor}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: backgroundColor),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await _loadPurchases();
    await _loadPendingPurchases();
  }

  Future<void> _loadPendingPurchases() async {
    if (!mounted) return;

    try {
      final pendingPurchases = await _databaseService.getPendingPurchases();

      // تصفية المشتريات المعلقة لهذا المستخدم فقط
      final userPendingPurchases =
          pendingPurchases
              .where((purchase) => purchase.userId == widget.user.id)
              .toList();

      if (!mounted) return;

      setState(() {
        _pendingPurchases = userPendingPurchases;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل المشتريات المعلقة: $e');
    }
  }

  @override
  void dispose() {
    _itemNameController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _loadPurchases() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // تحقق من صحة معرف المستخدم
      if (widget.user.id.isEmpty) {
        throw Exception('معرف المستخدم غير صالح');
      }

      debugPrint('بدء تحميل مشتريات المستخدم: ${widget.user.id}');
      final purchases = await _databaseService.getUserPurchases(widget.user.id);
      debugPrint('تم تحميل ${purchases.length} من المشتريات');

      if (!mounted) return;

      setState(() {
        _purchases = purchases;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('خطأ في تحميل المشتريات: $e');

      if (!mounted) return;

      setState(() {
        _isLoading = false;
        _purchases = [];
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ: $e'),
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('مشتريات ${widget.user.name}'),
        actions: [
          if (_pendingPurchases.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.sync, color: Colors.amber),
              onPressed: _syncPendingPurchases,
              tooltip: 'مزامنة المشتريات المعلقة (${_pendingPurchases.length})',
            ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          children: [
                            TextField(
                              controller: _itemNameController,
                              decoration: const InputDecoration(
                                labelText: 'اسم الغرض',
                              ),
                            ),
                            const SizedBox(height: 16),
                            TextField(
                              controller: _priceController,
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'السعر',
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () async {
                                if (_itemNameController.text.isNotEmpty &&
                                    _priceController.text.isNotEmpty) {
                                  final price =
                                      double.tryParse(_priceController.text) ??
                                      0;
                                  if (price > 0) {
                                    setState(() {
                                      _isLoading = true;
                                    });

                                    bool success = await _databaseService
                                        .addPurchase(
                                          widget.user.id,
                                          _itemNameController.text,
                                          price,
                                        );

                                    if (!mounted) return;
                                    if (success) {
                                      _itemNameController.clear();
                                      _priceController.clear();
                                      _showSnackBarSafely(
                                        'تمت إضافة المشتريات بنجاح',
                                      );
                                      await _loadData();
                                    } else {
                                      _showSnackBarSafely(
                                        'فشل في إضافة المشتريات. تحقق من اتصالك بالإنترنت.',
                                        backgroundColor: Colors.red,
                                      );
                                      setState(() {
                                        _isLoading = false;
                                      });
                                    }
                                  }
                                }
                              },
                              child: const Text('إضافة'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (_pendingPurchases.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.all(8),
                      color: Colors.amber.shade100,
                      child: Row(
                        children: [
                          const Icon(Icons.warning, color: Colors.orange),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'يوجد ${_pendingPurchases.length} مشتريات معلقة بانتظار الاتصال بالإنترنت',
                              style: const TextStyle(color: Colors.orange),
                            ),
                          ),
                          TextButton(
                            onPressed: _syncPendingPurchases,
                            child: const Text('مزامنة الآن'),
                          ),
                        ],
                      ),
                    ),
                  Expanded(
                    child:
                        _purchases.isEmpty && _pendingPurchases.isEmpty
                            ? const Center(child: Text('لا توجد مشتريات'))
                            : ListView(
                              children: [
                                // عرض المشتريات المعلقة
                                if (_pendingPurchases.isNotEmpty) ...[
                                  const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: Text(
                                      'المشتريات المعلقة',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  ..._pendingPurchases.map(
                                    (purchase) => Card(
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 4,
                                      ),
                                      color: Colors.grey.shade200,
                                      child: ListTile(
                                        title: Text(purchase.itemName),
                                        subtitle: Text(
                                          '${purchase.createdAt.day}/${purchase.createdAt.month}/${purchase.createdAt.year} (معلق)',
                                        ),
                                        trailing: Text(
                                          '${purchase.price} ل.س',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  const Divider(),
                                ],

                                // عرض المشتريات المزامنة
                                if (_purchases.isNotEmpty) ...[
                                  const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: Text(
                                      'المشتريات المزامنة',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                  ..._purchases.map(
                                    (purchase) => Card(
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 4,
                                      ),
                                      child: ListTile(
                                        title: Text(purchase.itemName),
                                        subtitle: Text(
                                          '${purchase.date.day}/${purchase.date.month}/${purchase.date.year}',
                                        ),
                                        trailing: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              '${purchase.price} ل.س',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            IconButton(
                                              icon: const Icon(
                                                Icons.edit,
                                                color: Colors.blue,
                                              ),
                                              onPressed: () {
                                                _showEditDialog(purchase);
                                              },
                                            ),
                                            IconButton(
                                              icon: const Icon(
                                                Icons.delete,
                                                color: Colors.red,
                                              ),
                                              onPressed: () {
                                                _showDeleteConfirmation(
                                                  purchase,
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    color: Colors.grey[200],
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الإجمالي:',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${_calculateTotal()} ل.س',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  void _showEditDialog(Purchase purchase) {
    final TextEditingController itemNameController = TextEditingController(
      text: purchase.itemName,
    );
    final TextEditingController priceController = TextEditingController(
      text: purchase.price.toString(),
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تعديل المشتريات'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: itemNameController,
                decoration: const InputDecoration(labelText: 'اسم الغرض'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(labelText: 'السعر'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed:
                  () => _handleEditPurchase(
                    context,
                    purchase,
                    itemNameController.text,
                    priceController.text,
                  ),
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleEditPurchase(
    BuildContext dialogContext,
    Purchase purchase,
    String itemName,
    String priceText,
  ) async {
    if (itemName.isNotEmpty && priceText.isNotEmpty) {
      final price = double.tryParse(priceText) ?? 0;
      if (price > 0) {
        setState(() {
          _isLoading = true;
        });
        Navigator.pop(dialogContext);

        bool success = await _databaseService.updatePurchase(
          purchase.id,
          itemName,
          price,
        );

        if (!mounted) return;
        if (success) {
          _showSnackBarSafely('تم تعديل المشتريات بنجاح');
          await _loadData();
        } else {
          setState(() {
            _isLoading = false;
          });
          _showSnackBarSafely(
            'فشل في تعديل المشتريات',
            backgroundColor: Colors.red,
          );
        }
      }
    }
  }

  void _showDeleteConfirmation(Purchase purchase) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذه المشتريات؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => _handleDeletePurchase(context, purchase),
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleDeletePurchase(
    BuildContext dialogContext,
    Purchase purchase,
  ) async {
    setState(() {
      _isLoading = true;
    });
    Navigator.pop(dialogContext);

    bool success = await _databaseService.deletePurchase(purchase.id);

    if (!mounted) return;
    if (success) {
      _showSnackBarSafely('تم حذف المشتريات بنجاح');
      await _loadData();
    } else {
      setState(() {
        _isLoading = false;
      });
      _showSnackBarSafely('فشل في حذف المشتريات', backgroundColor: Colors.red);
    }
  }

  // دالة لحساب الإجمالي بما في ذلك المشتريات المعلقة
  double _calculateTotal() {
    double total = _purchases.fold(
      0.0,
      (currentSum, purchase) => currentSum + purchase.price,
    );
    total += _pendingPurchases.fold(
      0.0,
      (currentSum, purchase) => currentSum + purchase.price,
    );
    return total;
  }

  // دالة لمزامنة المشتريات المعلقة
  Future<void> _syncPendingPurchases() async {
    if (_pendingPurchases.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    await _databaseService._syncPendingPurchases();

    // إعادة تحميل البيانات
    await _loadData();

    if (!mounted) return;
    _showSnackBarSafely('تمت مزامنة المشتريات المعلقة بنجاح');
  }
}

// شاشة جميع المشتريات
class AllPurchasesScreen extends StatefulWidget {
  const AllPurchasesScreen({super.key});

  @override
  State<AllPurchasesScreen> createState() => _AllPurchasesScreenState();
}

class _AllPurchasesScreenState extends State<AllPurchasesScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<Purchase> _purchases = [];
  List<AppUser> _users = [];
  bool _isLoading = true;

  // دالة مساعدة لإظهار SnackBar بشكل آمن
  void _showSnackBarSafely(String message, {Color? backgroundColor}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: backgroundColor),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final purchases = await _databaseService.getAllPurchases();
    final users = await _databaseService.getUsers();

    setState(() {
      _purchases = purchases;
      _users = users;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('جميع المشتريات')),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  Expanded(
                    child:
                        _purchases.isEmpty
                            ? const Center(child: Text('لا توجد مشتريات'))
                            : ListView.builder(
                              itemCount: _purchases.length,
                              itemBuilder: (context, index) {
                                final purchase = _purchases[index];
                                final user = _users.firstWhere(
                                  (user) => user.id == purchase.userId,
                                  orElse:
                                      () => AppUser(
                                        id: '',
                                        name: 'غير معروف',
                                        code: '',
                                      ),
                                );

                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 8,
                                  ),
                                  child: ListTile(
                                    title: Text(purchase.itemName),
                                    subtitle: Text(
                                      '${purchase.date.day}/${purchase.date.month}/${purchase.date.year} - ${user.name}',
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          '${purchase.price} ل.س',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        IconButton(
                                          icon: const Icon(
                                            Icons.edit,
                                            color: Colors.blue,
                                          ),
                                          onPressed: () {
                                            _showEditDialog(purchase, user);
                                          },
                                        ),
                                        IconButton(
                                          icon: const Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                          ),
                                          onPressed: () {
                                            _showDeleteConfirmation(purchase);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16.0),
                    color: Colors.grey[200],
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'الإجمالي:',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${_purchases.fold(0.0, (currentSum, purchase) => currentSum + purchase.price)} ل.س',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  void _showEditDialog(Purchase purchase, AppUser user) {
    final TextEditingController itemNameController = TextEditingController(
      text: purchase.itemName,
    );
    final TextEditingController priceController = TextEditingController(
      text: purchase.price.toString(),
    );

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تعديل مشتريات ${user.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: itemNameController,
                decoration: const InputDecoration(labelText: 'اسم الغرض'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(labelText: 'السعر'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed:
                  () => _handleEditPurchase(
                    context,
                    purchase,
                    itemNameController.text,
                    priceController.text,
                  ),
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleEditPurchase(
    BuildContext dialogContext,
    Purchase purchase,
    String itemName,
    String priceText,
  ) async {
    if (itemName.isNotEmpty && priceText.isNotEmpty) {
      final price = double.tryParse(priceText) ?? 0;
      if (price > 0) {
        setState(() {
          _isLoading = true;
        });
        Navigator.pop(dialogContext);

        bool success = await _databaseService.updatePurchase(
          purchase.id,
          itemName,
          price,
        );

        if (!mounted) return;
        if (success) {
          _showSnackBarSafely('تم تعديل المشتريات بنجاح');
          await _loadData();
        } else {
          setState(() {
            _isLoading = false;
          });
          _showSnackBarSafely(
            'فشل في تعديل المشتريات',
            backgroundColor: Colors.red,
          );
        }
      }
    }
  }

  void _showDeleteConfirmation(Purchase purchase) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف هذه المشتريات؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => _handleDeletePurchase(context, purchase),
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleDeletePurchase(
    BuildContext dialogContext,
    Purchase purchase,
  ) async {
    setState(() {
      _isLoading = true;
    });
    Navigator.pop(dialogContext);

    bool success = await _databaseService.deletePurchase(purchase.id);

    if (!mounted) return;
    if (success) {
      _showSnackBarSafely('تم حذف المشتريات بنجاح');
      await _loadData();
    } else {
      setState(() {
        _isLoading = false;
      });
      _showSnackBarSafely('فشل في حذف المشتريات', backgroundColor: Colors.red);
    }
  }
}

// شاشة الإحصائيات
class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final DatabaseService _databaseService = DatabaseService();
  List<Purchase> _purchases = [];
  List<AppUser> _users = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final purchases = await _databaseService.getAllPurchases();
    final users = await _databaseService.getUsers();

    setState(() {
      _purchases = purchases;
      _users = users;
      _isLoading = false;
    });
  }

  double _getUserTotal(String userId) {
    return _purchases
        .where((purchase) => purchase.userId == userId)
        .fold(0, (currentSum, purchase) => currentSum + purchase.price);
  }

  double get _totalAmount {
    return _purchases.fold(
      0,
      (currentSum, purchase) => currentSum + purchase.price,
    );
  }

  double get _amountPerUser {
    return _users.isEmpty ? 0 : _totalAmount / _users.length;
  }

  Map<String, double> _getBalances() {
    final Map<String, double> balances = {};

    for (var user in _users) {
      final userTotal = _getUserTotal(user.id);
      balances[user.id] = userTotal - _amountPerUser;
    }

    return balances;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('الإحصائيات')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final balances = _getBalances();

    return Scaffold(
      appBar: AppBar(title: const Text('الإحصائيات')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'إجمالي المشتريات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$_totalAmount ل.س',
                      style: const TextStyle(fontSize: 24),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'المبلغ المستحق لكل شخص',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '$_amountPerUser ل.س',
                      style: const TextStyle(fontSize: 24),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'تفاصيل المستخدمين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: _users.length,
                itemBuilder: (context, index) {
                  final user = _users[index];
                  final userTotal = _getUserTotal(user.id);
                  final balance = balances[user.id] ?? 0;

                  return Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            user.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('إجمالي المشتريات:'),
                              Text('$userTotal ل.س'),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              const Text('المبلغ المستحق:'),
                              Text('$_amountPerUser ل.س'),
                            ],
                          ),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                balance > 0 ? 'يستحق استلام:' : 'يجب أن يدفع:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      balance > 0 ? Colors.green : Colors.red,
                                ),
                              ),
                              Text(
                                '${balance.abs()} ل.س',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      balance > 0 ? Colors.green : Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
